import { Injectable, Logger, Inject, Optional } from "@nestjs/common";
import { InjectQueue } from "@nestjs/bull";
import { Queue, Job } from "bull";
import {
  DOMAIN_ANALYSIS_QUEUE,
  BATCH_ANALYSIS_QUEUE,
  API_RATE_LIMIT_QUEUE,
} from "./queue.module";

export interface DomainAnalysisJobData {
  targetDomainId: string;
  userId: string;
  priority?: number;
  userDomainId?: string;
  options?: {
    includeContactInfo?: boolean;
    includeKeywords?: boolean;
    includeTrafficData?: boolean;
  };
}

export interface BatchAnalysisJobData {
  targetDomainIds: string[];
  userId: string;
  batchId: string;
  userDomainId?: string;
  options?: {
    includeContactInfo?: boolean;
    includeKeywords?: boolean;
    includeTrafficData?: boolean;
    maxConcurrency?: number;
  };
}

export interface ApiCallJobData {
  provider: string;
  endpoint: string;
  params: Record<string, unknown>;
  retryCount?: number;
  domain: string;
  dataType: "domain-rating" | "traffic" | "keywords" | "backlinks" | "whois";
}

export interface QueueJobOptions {
  delay?: number;
  priority?: number;
  attempts?: number;
  backoff?: {
    type: "fixed" | "exponential";
    delay: number;
  };
}

export interface JobStatus {
  id: string;
  status: "waiting" | "active" | "completed" | "failed" | "delayed" | "paused";
  progress: number;
  data: unknown;
  result?: unknown;
  error?: string;
  createdAt: Date;
  processedAt?: Date;
  finishedAt?: Date;
  attempts: number;
  maxAttempts: number;
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @Optional()
    @InjectQueue(DOMAIN_ANALYSIS_QUEUE)
    private readonly domainAnalysisQueue?: Queue<DomainAnalysisJobData>,
    @Optional()
    @InjectQueue(BATCH_ANALYSIS_QUEUE)
    private readonly batchAnalysisQueue?: Queue<BatchAnalysisJobData>,
    @Optional()
    @InjectQueue(API_RATE_LIMIT_QUEUE)
    private readonly apiRateLimitQueue?: Queue<ApiCallJobData>
  ) {}

  /**
   * Add a domain analysis job to the queue
   */
  async addDomainAnalysisJob(
    data: DomainAnalysisJobData,
    options?: QueueJobOptions
  ): Promise<Job<DomainAnalysisJobData> | null> {
    if (!this.domainAnalysisQueue) {
      this.logger.warn("Domain analysis queue not available (Redis disabled)");
      return null;
    }

    try {
      const job = await this.domainAnalysisQueue.add("analyze-domain", data, {
        delay: options?.delay,
        priority: options?.priority || 0,
        attempts: options?.attempts || 3,
        backoff: options?.backoff || {
          type: "exponential",
          delay: 2000,
        },
      });

      this.logger.log(
        `Added domain analysis job ${job.id} for target domain ${data.targetDomainId}`
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add domain analysis job for ${data.targetDomainId}: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Add a batch analysis job to the queue
   */
  async addBatchAnalysisJob(
    data: BatchAnalysisJobData,
    options?: QueueJobOptions
  ): Promise<Job<BatchAnalysisJobData> | null> {
    if (!this.batchAnalysisQueue) {
      this.logger.warn("Batch analysis queue not available (Redis disabled)");
      return null;
    }

    try {
      const job = await this.batchAnalysisQueue.add("analyze-batch", data, {
        delay: options?.delay,
        priority: options?.priority || 0,
        attempts: options?.attempts || 3,
        backoff: options?.backoff || {
          type: "exponential",
          delay: 2000,
        },
      });

      this.logger.log(
        `Added batch analysis job ${job.id} for ${data.targetDomainIds.length} domains`
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add batch analysis job for batch ${data.batchId}: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Add an API call job with rate limiting
   */
  async addApiCallJob(
    data: ApiCallJobData,
    delay: number = 0
  ): Promise<Job<ApiCallJobData> | null> {
    if (!this.apiRateLimitQueue) {
      this.logger.warn("API rate limit queue not available (Redis disabled)");
      return null;
    }

    try {
      const job = await this.apiRateLimitQueue.add("api-call", data, {
        delay,
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 1000,
        },
      });

      this.logger.debug(
        `Added API call job ${job.id} for ${data.provider} with delay ${delay}ms`
      );

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to add API call job for ${data.provider}: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(
    queueName: string,
    jobId: string
  ): Promise<JobStatus | null> {
    try {
      let queue: Queue;

      switch (queueName) {
        case DOMAIN_ANALYSIS_QUEUE:
          queue = this.domainAnalysisQueue;
          break;
        case BATCH_ANALYSIS_QUEUE:
          queue = this.batchAnalysisQueue;
          break;
        case API_RATE_LIMIT_QUEUE:
          queue = this.apiRateLimitQueue;
          break;
        default:
          this.logger.warn(`Unknown queue name: ${queueName}`);
          return null;
      }

      const job = await queue.getJob(jobId);
      if (!job) {
        return null;
      }

      const jobState = await job.getState();
      return {
        id: job.id.toString(),
        status: jobState === "stuck" ? "failed" : jobState,
        progress: job.progress(),
        data: job.data,
        result: job.returnvalue,
        error: job.failedReason,
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts || 3,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get job status for ${jobId} in queue ${queueName}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  } | null> {
    try {
      let queue: Queue;

      switch (queueName) {
        case DOMAIN_ANALYSIS_QUEUE:
          queue = this.domainAnalysisQueue;
          break;
        case BATCH_ANALYSIS_QUEUE:
          queue = this.batchAnalysisQueue;
          break;
        case API_RATE_LIMIT_QUEUE:
          queue = this.apiRateLimitQueue;
          break;
        default:
          this.logger.warn(`Unknown queue name: ${queueName}`);
          return null;
      }

      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      // Check if queue is paused
      const isPaused = await queue.isPaused();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: isPaused ? 1 : 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get queue stats for ${queueName}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Pause a queue
   */
  async pauseQueue(queueName: string): Promise<void> {
    try {
      let queue: Queue;

      switch (queueName) {
        case DOMAIN_ANALYSIS_QUEUE:
          queue = this.domainAnalysisQueue;
          break;
        case BATCH_ANALYSIS_QUEUE:
          queue = this.batchAnalysisQueue;
          break;
        case API_RATE_LIMIT_QUEUE:
          queue = this.apiRateLimitQueue;
          break;
        default:
          throw new Error(`Unknown queue name: ${queueName}`);
      }

      await queue.pause();
      this.logger.log(`Paused queue: ${queueName}`);
    } catch (error) {
      this.logger.error(`Failed to pause queue ${queueName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Resume a queue
   */
  async resumeQueue(queueName: string): Promise<void> {
    try {
      let queue: Queue;

      switch (queueName) {
        case DOMAIN_ANALYSIS_QUEUE:
          queue = this.domainAnalysisQueue;
          break;
        case BATCH_ANALYSIS_QUEUE:
          queue = this.batchAnalysisQueue;
          break;
        case API_RATE_LIMIT_QUEUE:
          queue = this.apiRateLimitQueue;
          break;
        default:
          throw new Error(`Unknown queue name: ${queueName}`);
      }

      await queue.resume();
      this.logger.log(`Resumed queue: ${queueName}`);
    } catch (error) {
      this.logger.error(
        `Failed to resume queue ${queueName}: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Clean completed jobs from queue
   */
  async cleanQueue(
    queueName: string,
    grace: number = 5000,
    limit: number = 100
  ): Promise<void> {
    try {
      let queue: Queue;

      switch (queueName) {
        case DOMAIN_ANALYSIS_QUEUE:
          queue = this.domainAnalysisQueue;
          break;
        case BATCH_ANALYSIS_QUEUE:
          queue = this.batchAnalysisQueue;
          break;
        case API_RATE_LIMIT_QUEUE:
          queue = this.apiRateLimitQueue;
          break;
        default:
          throw new Error(`Unknown queue name: ${queueName}`);
      }

      await queue.clean(grace, "completed", limit);
      await queue.clean(grace, "failed", limit);
      this.logger.log(`Cleaned queue: ${queueName}`);
    } catch (error) {
      this.logger.error(`Failed to clean queue ${queueName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all queue names
   */
  getQueueNames(): string[] {
    return [DOMAIN_ANALYSIS_QUEUE, BATCH_ANALYSIS_QUEUE, API_RATE_LIMIT_QUEUE];
  }
}
