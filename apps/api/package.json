{"name": "api", "version": "0.0.1", "description": "BackLink API", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch --entryFile /apps/api/src/main.js", "dev:minimal": "nest start --watch --entryFile main-minimal", "start:ultra-minimal": "node start-minimal.js", "start:minimal": "node start-minimal.js", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:unit": "jest --testPathPattern=src/.*\\.spec\\.ts$", "test:integration": "jest --testPathPattern=test/.*\\.e2e-spec\\.ts$", "test:ci": "jest --coverage --watchAll=false", "test:all": "npm run test:unit && npm run test:integration", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/database/typeorm.config.ts", "migration:create": "typeorm-ts-node-commonjs migration:create", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/database/typeorm.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/database/typeorm.config.ts", "migration:show": "typeorm-ts-node-commonjs migration:show -d src/database/typeorm.config.ts", "schema:drop": "typeorm-ts-node-commonjs schema:drop -d src/database/typeorm.config.ts", "schema:sync": "typeorm-ts-node-commonjs schema:sync -d src/database/typeorm.config.ts"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/bull": "^10.2.3", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^7.1.8", "@nestjs/terminus": "^10.3.0", "@nestjs/throttler": "^4.2.1", "@nestjs/typeorm": "^10.0.0", "@types/cheerio": "^0.22.35", "axios": "^1.9.0", "bcrypt": "^5.1.0", "bull": "^4.16.5", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "csv-parser": "^3.2.0", "dns-packet": "^5.6.1", "helmet": "^7.0.0", "ioredis": "^5.3.2", "natural": "^8.0.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "puppeteer": "^24.8.2", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "robots-parser": "^3.0.1", "rxjs": "^7.8.1", "shared-types": "*", "typeorm": "^0.3.17"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/bull": "^4.10.0", "@types/cache-manager": "^4.0.2", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/csv-parser": "^3.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/redis": "^4.0.11", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "redis-memory-server": "^0.10.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig": "*", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}